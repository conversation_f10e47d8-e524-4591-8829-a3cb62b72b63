class KartensetManager {
    constructor() {
        this.currentTag = null;
        this.currentQuestionIndex = 0;
        this.modal = null;
        this.modalTitle = null;
        this.modalImage = null;
        this.modalQuestion = null;
        this.nextCardBtn = null;
        
        this.questions = {
            1: [
                "Was ist das größte ungestillte Bedürfnis in deinem Leben?",
                "Wenn über Nacht ein Wunder passiert und du findest dich morgen in deinem absoluten Traumleben wieder, was wäre dann anders?",
                "Gibt es aktuelle globale Herausforderungen, die dir besonders Sorgen bereiten?",
                "Gibt es gesellschaftliche oder soziale Themen, die dir besonders wichtig sind?",
                "Welche Werte sind dir wertvoll und wichtig im Leben?",
                "Was bringt dich im Leben am meisten zum Strahlen/Erblühen?",
                "Welche Herausforderungen beschäftigen dich im Moment in deinem Leben am meisten?",
                "Hast du schon mal \"spirituelle Impulse\" zu deiner Vision bekommen (z.B. über dein höheres Selbst, ein Medium, dein Geburtshoroskop o.ä.)?",
                "Welcher Vision würdest du nachgehen, wenn du unendlich viel Selbstvertrauen und Selbstbewusstsein hättest und dich nichts blockieren würde?",
                "Gibt es ein Thema, das dich seit Jahren immer wieder ruft?",
                "Was bedeutet Erfolg für dich ganz persönlich?"
            ],
            2: [
                "Welche großen Herausforderungen haben dein Leben geprägt und was hast du dabei gelernt?",
                "Gibt es Tätigkeiten, die du lange ausgeübt hast, die du auf keinen Fall wieder machen möchtest?",
                "Was schätzt du an dir selbst, das andere oft übersehen?",
                "Bist du eher ein rationaler Kopfmensch, ein intuitiver Gefühlsmensch oder eher instinktiv-körperlich?",
                "Kennst du deinen Persönlichkeitstyp (z.B. Human Design, 16 Personalities o.ä.) und was macht diesen aus?",
                "Was würdest du gerne können, das du an anderen bewunderst?",
                "Wenn Geld keine Rolle spielen würde und alles erfolgreich wäre, egal wie schräg die Idee ist, womit würdest du dann am liebsten deine Zeit verbringen?",
                "Wo in deinem Leben hast du schon mal jemanden tief inspiriert?",
                "Hast du Gaben, die noch \"roh\" sind und wachsen wollen, die du gerne mal hier in diesem sicheren Rahmen ausprobieren möchtest?",
                "Was machst du regelmäßig, das für dich normal ist, und für andere vielleicht schwer wäre?"
            ],
            3: [
                "Was möchten wir über die nächsten Tage gemeinsam co-kreieren (Ziel)?",
                "Gibt es ein ungestilltes Bedürfnis in der Gruppe, das wir gemeinsam stillen möchten?",
                "Gibt es ein kreatives Projekt, das wir umsetzen möchten?",
                "Was ist unser \"gemeinsamer Ruf\"?",
                "Wo überschneiden sich unsere Visionen auf kraftvolle Weise?",
                "Welche Fähigkeiten stehen uns als Gruppe zur Verfügung und wie können wir sie schlau einsetzen?",
                "Wer von uns hat einen Platz in welcher Geniezone?",
                "Wie fühlt sich unsere gemeinsame Essenz an, wenn wir sie nur mit einem Wort beschreiben?",
                "Gibt es einen Titel oder Satz, der unsere gemeinsame Ausrichtung beschreibt?",
                "Was haben wir gemeinsam und wo unterscheiden wir uns?",
                "Sind wir als Gruppe zueinander kompatibel (hinsichtlich unserer Werte, Visionen, Lebensvorstellungen)?"
            ],
            4: [
                "Welche Schwierigkeiten hast du generell in Gruppen und ist das in dieser Gruppe schon aufgetreten?",
                "Wo waren wir die letzten Tage irgendwie nicht so richtig um Flow und woran könnte es gelegen haben?",
                "Gibt es Momente, in denen du dich nicht so richtig verstanden gefühlt hast, und möchtest du deine Perspektive noch einmal ganz ausführlich erklären?",
                "Welche Hürden könnten dem Erreichen unseres Ziels im Wege stehen?",
                "Welche (noch unausgesprochenen) Erwartungen hast du an die Gruppe oder unser Projekt?",
                "Welche gesellschaftlichen Rollen und Erwartungen engen dich ein?",
                "Was in uns darf sich verändern, damit dieses Projekt gelingen kann?",
                "Gibt es etwas, das du ansprechen/aussprechen möchtest, aber dich bisher nicht getraut hast?",
                "Gibt es Gefühle, die du bisher zurückgehalten hast, denen wir gemeinsam Raum geben können?",
                "In welchen Momenten hast du dich in der Gruppe besonders verbunden gefühlt?",
                "Gibt es etwas, das wir tun können, damit du dich noch sicherer/freier fühlen kannst, du selbst zu sein?",
                "Worin möchtest du der Gruppe (noch) mehr vertrauen?",
                "Was brauchst du, um dich noch mehr einlassen zu können?"
            ],
            5: [
                "Wie wollen wir jetzt vorgehen?",
                "Auf welche Weise könnten wir unser Ziel angehen, die besonders viel Spaß macht?",
                "Angenommen, jeder macht nur das, was leicht fällt, was würde dann passieren?",
                "Gibt es innere Impulse, die wir spüren, denen wir nachgehen könnten?",
                "Welche Strukturen würden unserem Schwarm gut tun?"
            ],
            6: [
                "Gibt es etwas, das wir heute anders machen möchten, als gestern?",
                "Haben wir bisher eine Schwarmintelligenz wahrgenommen? Wie können wir darauf stärker zugreifen?",
                "Welche Richtung würde unser Projekt heute nehmen, wenn wir etwas Mutiges tun würden?"
            ],
            7: [
                "Wie gut ist es uns gelungen, zu co-kreieren?",
                "Wie hat sich die Gruppendynamik über die Zeit entwickelt?",
                "Was würdest du nächstes Mal anders machen?",
                "Was braucht es noch, um mit dem Projekt einen guten Abschluss zu finden?",
                "Wollen wir das Projekt auch nach dem Retreat weiterführen und was braucht es dafür noch?",
                "Wie hast du dich die letzten Tage gefühlt?",
                "Was war dein größter innerer Shift über die Woche?",
                "Was möchtest du aus dieser Erfahrung in dein Leben mitnehmen?",
                "Wofür bist du dankbar?",
                "Was möchtest du der Gruppe gerne rückmelden?",
                "Gibt es etwas, das noch ausgesprochen werden möchte?"
            ]
        };
        
        this.init();
    }
    
    init() {
        this.setupElements();
        this.setupEventListeners();
    }
    
    setupElements() {
        this.modal = document.getElementById('modalOverlay');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalImage = document.getElementById('modalImage');
        this.modalQuestion = document.getElementById('modalQuestion');
        this.nextCardBtn = document.getElementById('nextCardBtn');
    }
    
    setupEventListeners() {
        // Kartenstapel Click Events
        const kartenstapel = document.querySelectorAll('.kartenstapel');
        kartenstapel.forEach(stapel => {
            stapel.addEventListener('click', (e) => {
                const tag = parseInt(e.currentTarget.getAttribute('data-tag'));
                this.openModal(tag);
            });
        });
        
        // Modal Close Events
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.closeModal();
            }
        });
        
        // Next Card Button
        this.nextCardBtn.addEventListener('click', () => {
            this.showNextCard();
        });
        
        // Escape Key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('active')) {
                this.closeModal();
            }
        });
    }
    
    openModal(tag) {
        this.currentTag = tag;
        this.currentQuestionIndex = 0;
        
        // Set modal title
        const tagTitles = {
            1: "TAG 1: DEN RUF ERKUNDEN",
            2: "TAG 2: DAS LICHT ERKUNDEN", 
            3: "TAG 3: DAS GEMEINSAME PUZZLE",
            4: "TAG 4: GRUPPENDYNAMIKEN UND HÜRDEN LÖSEN",
            5: "TAG 5: ZUM SCHWARM WERDEN",
            6: "TAG 6: IM FLOW",
            7: "TAG 7: ABSCHLUSS UND REFLEXION"
        };
        
        this.modalTitle.textContent = tagTitles[tag];
        
        // Show first card
        this.showCurrentCard();
        
        // Show modal
        this.modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    closeModal() {
        this.modal.classList.remove('active');
        document.body.style.overflow = '';
        this.currentTag = null;
        this.currentQuestionIndex = 0;
    }
    
    showCurrentCard() {
        if (!this.currentTag || !this.questions[this.currentTag]) return;
        
        const questions = this.questions[this.currentTag];
        const question = questions[this.currentQuestionIndex];
        
        // Calculate image number based on tag and question index
        const imageNumber = this.getImageNumber(this.currentTag, this.currentQuestionIndex);
        
        // Set image and question
        this.modalImage.src = `assets/bilder/Fragen/${imageNumber}.png`;
        this.modalImage.alt = `Fragenkarte ${imageNumber}`;
        this.modalQuestion.textContent = question;
        
        // Update button text
        if (this.currentQuestionIndex >= questions.length - 1) {
            this.nextCardBtn.textContent = "MODAL SCHLIESSEN";
        } else {
            this.nextCardBtn.textContent = "NEUE KARTE ZIEHEN";
        }
    }
    
    showNextCard() {
        if (!this.currentTag || !this.questions[this.currentTag]) return;
        
        const questions = this.questions[this.currentTag];
        
        if (this.currentQuestionIndex >= questions.length - 1) {
            // Last question reached, close modal
            this.closeModal();
            return;
        }
        
        this.currentQuestionIndex++;
        this.showCurrentCard();
    }
    
    getImageNumber(tag, questionIndex) {
        // Calculate the absolute image number based on tag and question index
        const tagStartNumbers = {
            1: 1,   // Tag 1: Questions 1-11 (Images 1-11)
            2: 12,  // Tag 2: Questions 12-21 (Images 12-21) 
            3: 22,  // Tag 3: Questions 22-32 (Images 22-32)
            4: 33,  // Tag 4: Questions 33-45 (Images 33-45)
            5: 46,  // Tag 5: Questions 46-50 (Images 46-50)
            6: 51,  // Tag 6: Questions 51-53 (Images 51-53)
            7: 54   // Tag 7: Questions 54-62 (Images 54-62)
        };
        
        return tagStartNumbers[tag] + questionIndex;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new KartensetManager();
});
